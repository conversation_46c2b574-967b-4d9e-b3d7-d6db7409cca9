<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".NormalPreviewActivity">

    <!-- 相机预览视图 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/previewView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:scaleType="fillCenter" />

    <!-- 顶部控制栏 -->
    <LinearLayout
        android:id="@+id/topControlBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@drawable/gradient_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="返回"
            android:tint="@android:color/white" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 切换摄像头按钮 -->
        <ImageButton
            android:id="@+id/switchCameraButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_switch_camera"
            android:contentDescription="切换摄像头"
            android:tint="@android:color/white" />

    </LinearLayout>

    <!-- 底部控制栏 -->
    <RelativeLayout
        android:id="@+id/bottomControlBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="24dp"
        android:background="@drawable/gradient_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 左下角缩略图预览 -->
        <FrameLayout
            android:id="@+id/thumbnailContainer"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="@drawable/thumbnail_background"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone">

            <ImageView
                android:id="@+id/thumbnailImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:contentDescription="最近拍摄的照片" />

        </FrameLayout>

        <!-- 拍照按钮 -->
        <View
            android:id="@+id/captureButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:background="@drawable/capture_button_background"
            android:clickable="true"
            android:focusable="true" />

    </RelativeLayout>

    <!-- 拍照闪光效果 -->
    <View
        android:id="@+id/captureFlash"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@android:color/white"
        android:alpha="0"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
