<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    tools:context=".NormalNoPreviewActivity">

    <!-- 顶部控制栏 -->
    <LinearLayout
        android:id="@+id/topControls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="返回"
            android:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="正常设备后台拍照"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

        <!-- 切换摄像头按钮 -->
        <ImageButton
            android:id="@+id/switchCameraButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_switch_camera"
            android:contentDescription="切换摄像头"
            android:tint="@android:color/white" />

    </LinearLayout>

    <!-- 状态显示区域 -->
    <LinearLayout
        android:id="@+id/statusArea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/topControls"
        android:layout_marginTop="32dp"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="24dp">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_camera"
            android:tint="@android:color/white"
            android:alpha="0.6" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="正常设备后台拍照模式"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="无需预览，直接从相机流获取图像"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:alpha="0.8" />

        <TextView
            android:id="@+id/cameraStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="正在初始化摄像头..."
            android:textColor="#FFA726"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- 底部控制栏 -->
    <LinearLayout
        android:id="@+id/bottomControls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:padding="24dp"
        android:gravity="center">

        <!-- 缩略图容器 -->
        <FrameLayout
            android:id="@+id/thumbnailContainer"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginEnd="32dp"
            android:background="@drawable/thumbnail_background"
            android:visibility="gone">

            <ImageView
                android:id="@+id/thumbnailImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop" />

        </FrameLayout>

        <!-- 拍照按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/captureButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_camera_capture"
            app:fabSize="normal"
            app:backgroundTint="@android:color/white"
            app:tint="@android:color/black"
            android:contentDescription="拍照" />

        <!-- 占位空间，保持拍照按钮居中 -->
        <View
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginStart="32dp" />

    </LinearLayout>

    <!-- 拍照闪光效果 -->
    <View
        android:id="@+id/captureFlash"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:visibility="gone"
        android:alpha="0" />

</RelativeLayout>
