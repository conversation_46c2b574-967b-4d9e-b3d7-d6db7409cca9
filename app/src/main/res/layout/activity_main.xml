<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".MainActivity">

    <LinearLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 标题 -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:text="相机功能选择"
            android:textColor="@color/design_default_color_primary"
            android:textSize="28sp"
            android:textStyle="bold" />

        <!-- 说明文字 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text="请选择适合你设备的拍照模式："
            android:textColor="@android:color/darker_gray"
            android:textSize="16sp" />

        <!-- 正常设备分组 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="📱 正常手机/平板设备"
            android:textColor="@color/design_default_color_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 正常设备有预览按钮 -->
        <Button
            android:id="@+id/normalPreviewButton"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="12dp"
            android:backgroundTint="#2196F3"
            android:drawableStart="@drawable/ic_camera"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:text="有预览模式拍照"
            android:textSize="16sp" />

        <!-- 正常设备无预览按钮 -->
        <Button
            android:id="@+id/normalNoPreviewButton"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="32dp"
            android:backgroundTint="#03A9F4"
            android:drawableStart="@drawable/ic_camera_capture"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:text="无预览模式拍照"
            android:textSize="16sp" />

        <!-- USB设备分组 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="🔌 固定设备USB摄像头"
            android:textColor="#4CAF50"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- USB设备有预览按钮 -->
        <Button
            android:id="@+id/usbPreviewButton"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="12dp"
            android:backgroundTint="#4CAF50"
            android:drawableStart="@drawable/ic_camera"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:text="有预览模式拍照"
            android:textSize="16sp" />

        <!-- USB设备无预览按钮 -->
        <Button
            android:id="@+id/usbNoPreviewButton"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="24dp"
            android:backgroundTint="#66BB6A"
            android:drawableStart="@drawable/ic_camera_capture"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:text="无预览模式拍照"
            android:textSize="16sp" />
    </LinearLayout>

</ScrollView>
