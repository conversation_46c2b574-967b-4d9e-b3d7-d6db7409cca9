<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".DualScreenTestActivity">

    <LinearLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Button
                android:id="@+id/backButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:text="←"
                android:textColor="@color/design_default_color_primary"
                android:textSize="20sp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="双屏设备测试"
                android:textColor="@color/design_default_color_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>

        <!-- 设备信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="📱 设备信息"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/deviceInfoText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="4dp"
                    android:text="正在检测设备信息..."
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 双屏状态卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="🖥️ 双屏状态"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/dualScreenStatusText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="4dp"
                    android:text="正在检测双屏支持..."
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 控制按钮区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="🎮 测试控制"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- 检测双屏按钮 -->
                <Button
                    android:id="@+id/detectScreensButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#4CAF50"
                    android:text="🔍 检测屏幕信息"
                    android:textSize="16sp" />

                <!-- 显示红色测试按钮 -->
                <Button
                    android:id="@+id/showRedTestButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#F44336"
                    android:text="🔴 副屏显示红色"
                    android:textSize="16sp" />

                <!-- 显示绿色测试按钮 -->
                <Button
                    android:id="@+id/showGreenTestButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#4CAF50"
                    android:text="🟢 副屏显示绿色"
                    android:textSize="16sp" />

                <!-- 显示蓝色测试按钮 -->
                <Button
                    android:id="@+id/showBlueTestButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#2196F3"
                    android:text="🔵 副屏显示蓝色"
                    android:textSize="16sp" />

                <!-- 显示文字测试按钮 -->
                <Button
                    android:id="@+id/showTextTestButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#FF9800"
                    android:text="📝 副屏显示文字"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/showXMLTestButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#FF9800"
                    android:text="📝 副屏XML Layout"
                    android:textSize="16sp" />

                <!-- 交互测试按钮 -->
                <Button
                    android:id="@+id/showInteractiveTestButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#9C27B0"
                    android:text="🎮 副屏交互测试"
                    android:textSize="16sp" />

                <!-- 发送消息按钮 -->
                <Button
                    android:id="@+id/sendMessageButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:backgroundTint="#607D8B"
                    android:text="📤 发送消息到副屏"
                    android:textSize="16sp" />

                <!-- 清空副屏按钮 -->
                <Button
                    android:id="@+id/clearSecondaryScreenButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:backgroundTint="#9E9E9E"
                    android:text="🗑️ 清空副屏内容"
                    android:textSize="16sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>
    </LinearLayout>

</ScrollView>
