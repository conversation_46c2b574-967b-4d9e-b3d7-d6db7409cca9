package com.ygxj.camerademo.dualscreen

/**
 * 点击事件处理接口
 * 用于主副屏之间的事件通信
 */
interface ClickEventHandler {
    
    /**
     * 副屏按钮点击事件
     * @param buttonId 按钮ID
     * @param buttonText 按钮文字
     */
    fun onSecondaryScreenButtonClick(buttonId: String, buttonText: String)
    
    /**
     * 副屏区域点击事件
     * @param x 点击的X坐标
     * @param y 点击的Y坐标
     */
    fun onSecondaryScreenAreaClick(x: Float, y: Float)
    
    /**
     * 副屏长按事件
     * @param duration 长按持续时间（毫秒）
     */
    fun onSecondaryScreenLongPress(duration: Long)
    
    /**
     * 副屏滑动事件
     * @param direction 滑动方向
     * @param distance 滑动距离
     */
    fun onSecondaryScreenSwipe(direction: String, distance: Float)
}
