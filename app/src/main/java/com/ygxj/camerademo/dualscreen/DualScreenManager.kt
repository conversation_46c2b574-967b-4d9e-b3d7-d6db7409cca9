package com.ygxj.camerademo.dualscreen

import android.content.Context
import android.graphics.Color
import android.hardware.display.DisplayManager
import android.view.Display

/**
 * 双屏管理器
 * 负责管理双屏显示相关的功能
 */
class DualScreenManager(
    private val context: Context,
    private val displayManager: DisplayManager
) {

    private var secondaryPresentation: SecondaryScreenPresentation? = null
    private var interactivePresentation: InteractivePresentation? = null

    /**
     * 获取双屏状态信息
     */
    fun getDualScreenStatus(): String {
        val displays = displayManager.displays
        val statusText = StringBuilder()
        
        if (displays != null && displays.size > 1) {
            statusText.append("✅ 检测到多个显示器\n")
            statusText.append("显示器数量: ${displays.size}\n\n")
            
            displays.forEachIndexed { index, display ->
                statusText.append("显示器 $index:\n")
                statusText.append("  ID: ${display.displayId}\n")
                statusText.append("  名称: ${display.name}\n")
                statusText.append("  状态: ${getDisplayState(display.state)}\n")
                statusText.append("  类型: ${getDisplayType(display)}\n\n")
            }
        } else {
            statusText.append("❌ 未检测到多个显示器\n")
            statusText.append("当前设备可能不支持双屏显示\n")
            statusText.append("或者副屏未连接/未启用")
        }
        
        return statusText.toString()
    }

    /**
     * 获取所有屏幕信息
     */
    fun getAllScreensInfo(): String {
        val displays = displayManager.displays
        val statusText = StringBuilder()
        
        if (displays != null) {
            statusText.append("✅ 检测到多个显示器\n")
            statusText.append("显示器数量: ${displays.size}\n\n")
            
            displays.forEachIndexed { index, display ->
                val metrics = android.util.DisplayMetrics()
                display.getMetrics(metrics)
                
                statusText.append("显示器 $index:\n")
                statusText.append("  ID: ${display.displayId}\n")
                statusText.append("  名称: ${display.name}\n")
                statusText.append("  分辨率: ${metrics.widthPixels}x${metrics.heightPixels}\n")
                statusText.append("  密度: ${metrics.density}\n")
                statusText.append("  DPI: ${metrics.densityDpi}\n")
                statusText.append("  状态: ${getDisplayState(display.state)}\n")
                statusText.append("  类型: ${getDisplayType(display)}\n\n")
            }
        } else {
            statusText.append("❌ 无法获取显示器信息")
        }
        
        return statusText.toString()
    }

    /**
     * 在副屏显示颜色
     */
    fun showColorOnSecondaryScreen(color: Int, colorName: String): Boolean {
        val displays = displayManager.displays
        
        if (displays != null && displays.size > 1) {
            val secondaryDisplay = displays[1] // 使用第二个显示器
            
            // 关闭之前的Presentation
            secondaryPresentation?.dismiss()
            
            // 创建新的Presentation显示颜色
            secondaryPresentation = SecondaryScreenPresentation(
                context, 
                secondaryDisplay, 
                color, 
                colorName, 
                false
            )
            secondaryPresentation?.show()
            
            return true
        }
        
        return false
    }

    /**
     * 在副屏显示文字
     */
    fun showTextOnSecondaryScreen(): Boolean {
        val displays = displayManager.displays
        
        if (displays != null && displays.size > 1) {
            val secondaryDisplay = displays[1]
            
            // 关闭之前的Presentation
            secondaryPresentation?.dismiss()
            
            // 创建显示文字的Presentation
            secondaryPresentation = SecondaryScreenPresentation(
                context, 
                secondaryDisplay, 
                Color.WHITE, 
                "文字测试", 
                true
            )
            secondaryPresentation?.show()
            
            return true
        }
        
        return false
    }

    /**
     * 清空副屏内容
     */
    fun clearSecondaryScreen() {
        secondaryPresentation?.dismiss()
        secondaryPresentation = null
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        secondaryPresentation?.dismiss()
        secondaryPresentation = null
    }

    /**
     * 获取显示器状态描述
     */
    private fun getDisplayState(state: Int): String {
        return when (state) {
            Display.STATE_OFF -> "关闭"
            Display.STATE_ON -> "开启"
            Display.STATE_DOZE -> "低功耗"
            Display.STATE_DOZE_SUSPEND -> "低功耗暂停"
            Display.STATE_VR -> "VR模式"
            Display.STATE_ON_SUSPEND -> "开启暂停"
            else -> "未知($state)"
        }
    }

    /**
     * 获取显示器类型描述
     */
    private fun getDisplayType(display: Display): String {
        return if (display.displayId == 0) {
            "主显示器"
        } else {
            "副显示器"
        }
    }
}
