package com.ygxj.camerademo.dualscreen

import android.app.Presentation
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Display
import android.view.GestureDetector
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import java.text.SimpleDateFormat
import java.util.*

/**
 * 支持交互的副屏Presentation
 * 包含各种点击、触摸事件处理
 */
class InteractivePresentation(
    context: Context,
    display: Display,
    private val clickEventHandler: ClickEventHandler?
) : Presentation(context, display) {

    private lateinit var mainLayout: LinearLayout
    private lateinit var statusText: TextView
    private lateinit var coordinateText: TextView
    private lateinit var gestureDetector: GestureDetector
    private var clickCount = 0
    private var longPressStartTime = 0L
    private var isLongPressDetected = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupGestureDetector()
        createInteractiveLayout()
        setupWindow()
    }

    private fun setupGestureDetector() {
        gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            
            override fun onSingleTapUp(e: MotionEvent): Boolean {
                if (!isLongPressDetected) {
                    handleAreaClick(e.x, e.y)
                }
                isLongPressDetected = false
                return true
            }

            override fun onLongPress(e: MotionEvent) {
                isLongPressDetected = true
                val duration = System.currentTimeMillis() - longPressStartTime
                handleLongPress(duration)
            }
            
            override fun onFling(
                e1: MotionEvent?,
                e2: MotionEvent,
                velocityX: Float,
                velocityY: Float
            ): Boolean {
                if (e1 != null) {
                    val deltaX = e2.x - e1.x
                    val deltaY = e2.y - e1.y
                    val distance = kotlin.math.sqrt((deltaX * deltaX + deltaY * deltaY).toDouble()).toFloat()
                    
                    val direction = when {
                        kotlin.math.abs(deltaX) > kotlin.math.abs(deltaY) -> {
                            if (deltaX > 0) "右滑" else "左滑"
                        }
                        else -> {
                            if (deltaY > 0) "下滑" else "上滑"
                        }
                    }
                    
                    handleSwipe(direction, distance)
                }
                return true
            }
        })
    }

    private fun createInteractiveLayout() {
        mainLayout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setBackgroundColor(Color.parseColor("#E3F2FD"))
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            )
            setPadding(32, 32, 32, 32)
        }

        // 标题
        val titleText = TextView(context).apply {
            text = "副屏交互测试"
            textSize = 28f
            setTextColor(Color.parseColor("#1976D2"))
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 24)
        }

        // 状态显示
        statusText = TextView(context).apply {
            text = "等待交互..."
            textSize = 16f
            setTextColor(Color.parseColor("#424242"))
            gravity = Gravity.CENTER
            setPadding(16, 16, 16, 16)
            setBackgroundColor(Color.parseColor("#F5F5F5"))
        }

        // 坐标显示
        coordinateText = TextView(context).apply {
            text = "坐标: (0, 0)"
            textSize = 14f
            setTextColor(Color.parseColor("#666666"))
            gravity = Gravity.CENTER
            setPadding(0, 16, 0, 24)
        }

        // 按钮区域
        val buttonContainer = createButtonContainer()

        // 触摸区域
        val touchArea = createTouchArea()

        // 添加所有视图
        mainLayout.addView(titleText)
        mainLayout.addView(statusText)
        mainLayout.addView(coordinateText)
        mainLayout.addView(buttonContainer)
        mainLayout.addView(touchArea)

        setContentView(mainLayout)
    }

    private fun createButtonContainer(): LinearLayout {
        val container = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 24)
        }

        // 按钮1
        val button1 = Button(context).apply {
            text = "按钮1"
            setBackgroundColor(Color.parseColor("#4CAF50"))
            setTextColor(Color.WHITE)
            setPadding(24, 16, 24, 16)
            setOnClickListener {
                handleButtonClick("button1", "按钮1")
            }
        }

        // 按钮2
        val button2 = Button(context).apply {
            text = "按钮2"
            setBackgroundColor(Color.parseColor("#FF9800"))
            setTextColor(Color.WHITE)
            setPadding(24, 16, 24, 16)
            setOnClickListener {
                handleButtonClick("button2", "按钮2")
            }
        }

        // 按钮3
        val button3 = Button(context).apply {
            text = "重置"
            setBackgroundColor(Color.parseColor("#F44336"))
            setTextColor(Color.WHITE)
            setPadding(24, 16, 24, 16)
            setOnClickListener {
                handleButtonClick("reset", "重置")
                resetStatus()
            }
        }

        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            setMargins(8, 0, 8, 0)
        }

        button1.layoutParams = layoutParams
        button2.layoutParams = layoutParams
        button3.layoutParams = layoutParams

        container.addView(button1)
        container.addView(button2)
        container.addView(button3)

        return container
    }

    private fun createTouchArea(): View {
        return View(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                200
            )
            setBackgroundColor(Color.parseColor("#BBDEFB"))
            
            setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        longPressStartTime = System.currentTimeMillis()
                        isLongPressDetected = false
                    }
                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        isLongPressDetected = false
                    }
                }
                gestureDetector.onTouchEvent(event)
                true
            }
        }
    }

    private fun setupWindow() {
        window?.let { window ->
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    private fun handleButtonClick(buttonId: String, buttonText: String) {
        clickCount++
        updateStatus("按钮点击: $buttonText (第${clickCount}次)")
        clickEventHandler?.onSecondaryScreenButtonClick(buttonId, buttonText)
    }

    private fun handleAreaClick(x: Float, y: Float) {
        updateCoordinate(x, y)
        updateStatus("区域点击: (${x.toInt()}, ${y.toInt()})")
        clickEventHandler?.onSecondaryScreenAreaClick(x, y)
    }

    private fun handleLongPress(duration: Long) {
        updateStatus("长按事件: 持续${duration}毫秒")
        clickEventHandler?.onSecondaryScreenLongPress(duration)
    }

    private fun handleSwipe(direction: String, distance: Float) {
        updateStatus("滑动事件: $direction, 距离${distance.toInt()}px")
        clickEventHandler?.onSecondaryScreenSwipe(direction, distance)
    }

    private fun updateStatus(message: String) {
        try {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            statusText.text = "[$timestamp] $message"
        } catch (e: Exception) {
            // 防止在Presentation销毁后更新UI导致的异常
        }
    }

    private fun updateCoordinate(x: Float, y: Float) {
        try {
            coordinateText.text = "坐标: (${x.toInt()}, ${y.toInt()})"
        } catch (e: Exception) {
            // 防止在Presentation销毁后更新UI导致的异常
        }
    }

    private fun resetStatus() {
        clickCount = 0
        statusText.text = "状态已重置"
        coordinateText.text = "坐标: (0, 0)"
    }

    /**
     * 外部调用更新显示内容
     */
    fun updateMessage(message: String) {
        updateStatus("主屏消息: $message")
    }

    override fun onStop() {
        super.onStop()
        // 清理资源，防止内存泄漏
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 确保在窗口分离时清理资源
    }
}
