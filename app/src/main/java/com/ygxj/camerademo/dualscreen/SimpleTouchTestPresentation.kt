package com.ygxj.camerademo.dualscreen

import android.app.Presentation
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Display
import android.view.Gravity
import android.view.MotionEvent
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import java.text.SimpleDateFormat
import java.util.*

/**
 * 简化的触摸测试Presentation
 * 专门用于测试副屏触摸事件是否正确响应
 */
class SimpleTouchTestPresentation(
    context: Context,
    display: Display,
    private val clickEventHandler: ClickEventHandler?
) : Presentation(context, display) {

    private lateinit var statusText: TextView
    private lateinit var coordinateText: TextView
    private var touchCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        createSimpleLayout()
        setupWindow()
    }

    private fun createSimpleLayout() {
        val mainLayout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setBackgroundColor(Color.parseColor("#E8F5E8"))
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            )
            setPadding(32, 32, 32, 32)
            
            // 设置整个布局可以接收触摸事件
            isFocusable = true
            isFocusableInTouchMode = true
            isClickable = true
        }

        // 标题
        val titleText = TextView(context).apply {
            text = "副屏触摸测试"
            textSize = 32f
            setTextColor(Color.parseColor("#2E7D32"))
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 32)
        }

        // 说明文字
        val instructionText = TextView(context).apply {
            text = "请在此屏幕上任意位置点击\n测试触摸事件是否正确响应"
            textSize = 18f
            setTextColor(Color.parseColor("#424242"))
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 32)
        }

        // 状态显示
        statusText = TextView(context).apply {
            text = "等待触摸..."
            textSize = 20f
            setTextColor(Color.parseColor("#1976D2"))
            gravity = Gravity.CENTER
            setPadding(16, 16, 16, 16)
            setBackgroundColor(Color.parseColor("#E3F2FD"))
        }

        // 坐标显示
        coordinateText = TextView(context).apply {
            text = "坐标: (0, 0)\n触摸次数: 0"
            textSize = 16f
            setTextColor(Color.parseColor("#666666"))
            gravity = Gravity.CENTER
            setPadding(0, 32, 0, 0)
        }

        // 添加所有视图
        mainLayout.addView(titleText)
        mainLayout.addView(instructionText)
        mainLayout.addView(statusText)
        mainLayout.addView(coordinateText)

        // 设置整个布局的触摸监听器
        mainLayout.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    handleTouch(event.x, event.y, "按下")
                    view.requestFocus()
                    view.requestFocusFromTouch()
                }
                MotionEvent.ACTION_UP -> {
                    handleTouch(event.x, event.y, "抬起")
                }
                MotionEvent.ACTION_MOVE -> {
                    handleTouch(event.x, event.y, "移动")
                }
            }
            // 返回true表示事件已被处理，不会传递给其他视图
            true
        }

        setContentView(mainLayout)
    }

    private fun setupWindow() {
        window?.let { window ->
            // 基本窗口设置
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            
            // 确保窗口可以接收触摸事件
            window.addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL)
            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            
            // 硬件加速
            window.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
            
            // 设置窗口属性
            val layoutParams = window.attributes
            layoutParams.flags = layoutParams.flags or WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            window.attributes = layoutParams
        }
    }

    private fun handleTouch(x: Float, y: Float, action: String) {
        touchCount++
        
        val timestamp = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(Date())
        val message = "[$timestamp] $action 事件"
        
        try {
            statusText.text = message
            coordinateText.text = "坐标: (${x.toInt()}, ${y.toInt()})\n触摸次数: $touchCount"
            
            // 通知主屏
            clickEventHandler?.onSecondaryScreenAreaClick(x, y)
            
        } catch (e: Exception) {
            // 防止UI更新异常
        }
    }

    /**
     * 重置计数器
     */
    fun resetCounter() {
        touchCount = 0
        try {
            statusText.text = "计数器已重置"
            coordinateText.text = "坐标: (0, 0)\n触摸次数: 0"
        } catch (e: Exception) {
            // 防止UI更新异常
        }
    }

    override fun onStop() {
        super.onStop()
        // 清理资源
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 确保在窗口分离时清理资源
    }
}
