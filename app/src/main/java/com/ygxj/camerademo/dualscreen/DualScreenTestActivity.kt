package com.ygxj.camerademo.dualscreen

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.ygxj.camerademo.databinding.ActivityDualScreenTestBinding

/**
 * 双屏设备测试Activity
 * 用于测试和验证双屏设备的显示功能
 */
class DualScreenTestActivity : AppCompatActivity(), ClickEventHandler {

    private lateinit var binding: ActivityDualScreenTestBinding
    private lateinit var dualScreenManager: DualScreenManager
    private lateinit var deviceInfoHelper: DeviceInfoHelper
    private var interactionCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        binding = ActivityDualScreenTestBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initializeComponents()
        setupClickListeners()
        updateDeviceInfo()
        updateDualScreenStatus()
    }

    private fun initializeComponents() {
        val displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        dualScreenManager = DualScreenManager(this, displayManager)
        deviceInfoHelper = DeviceInfoHelper(this)
    }

    private fun setupClickListeners() {
        // 返回按钮
        binding.backButton.setOnClickListener {
            finish()
        }

        // 检测屏幕信息按钮
        binding.detectScreensButton.setOnClickListener {
            detectAllScreens()
        }

        // 显示红色测试按钮
        binding.showRedTestButton.setOnClickListener {
            showColorTest(android.graphics.Color.RED, "红色测试")
        }

        // 显示绿色测试按钮
        binding.showGreenTestButton.setOnClickListener {
            showColorTest(android.graphics.Color.GREEN, "绿色测试")
        }

        // 显示蓝色测试按钮
        binding.showBlueTestButton.setOnClickListener {
            showColorTest(android.graphics.Color.BLUE, "蓝色测试")
        }

        // 显示文字测试按钮
        binding.showTextTestButton.setOnClickListener {
            showTextTest()
        }

        // XML布局测试按钮
        binding.showXMLTestButton.setOnClickListener {
            showXMLLayoutTest()
        }

        // 交互测试按钮
        binding.showInteractiveTestButton.setOnClickListener {
            showInteractiveTest()
        }

        // 发送消息按钮
        binding.sendMessageButton.setOnClickListener {
            sendMessageToSecondaryScreen()
        }

        // 清空副屏按钮
        binding.clearSecondaryScreenButton.setOnClickListener {
            clearSecondaryScreen()
        }
    }

    private fun setupInteractionButtons() {
        // 这里可以添加更多交互测试按钮
        // 例如：发送消息到副屏的按钮等
    }

    private fun updateDeviceInfo() {
        val deviceInfo = deviceInfoHelper.getDeviceInfo()
        binding.deviceInfoText.text = deviceInfo
    }

    private fun updateDualScreenStatus() {
        val statusInfo = dualScreenManager.getDualScreenStatus()
        binding.dualScreenStatusText.text = statusInfo
    }

    private fun detectAllScreens() {
        val screenInfo = dualScreenManager.getAllScreensInfo()
        binding.dualScreenStatusText.text = screenInfo
    }

    private fun showColorTest(color: Int, colorName: String) {
        val success = dualScreenManager.showColorOnSecondaryScreen(color, colorName)
        if (!success) {
            Toast.makeText(this, "未检测到副屏，无法显示内容", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showTextTest() {
        val success = dualScreenManager.showTextOnSecondaryScreen()
        if (!success) {
            Toast.makeText(this, "未检测到副屏，无法显示内容", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showXMLLayoutTest() {
        val success = dualScreenManager.showXMLLayoutScreen()
        if (!success) {
            Toast.makeText(this, "未检测到副屏，无法显示XML布局", Toast.LENGTH_SHORT).show()
        }
    }



    private fun showInteractiveTest() {
        val success = dualScreenManager.showInteractiveScreen(this)
        if (!success) {
            Toast.makeText(this, "未检测到副屏，无法显示交互界面", Toast.LENGTH_SHORT).show()
        }
    }

    private fun sendMessageToSecondaryScreen() {
        interactionCount++
        val message = "来自主屏的消息 #$interactionCount"
        dualScreenManager.sendMessageToInteractiveScreen(message)
    }

    private fun clearSecondaryScreen() {
        dualScreenManager.clearSecondaryScreen()
    }

    // 实现ClickEventHandler接口
    override fun onSecondaryScreenButtonClick(buttonId: String, buttonText: String) {
        runOnUiThread {
            val message = "副屏按钮点击: $buttonText (ID: $buttonId)"
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()

            // 可以根据buttonId执行不同的操作
            when (buttonId) {
                "button1" -> {
                    // 处理按钮1的点击
                    sendMessageToSecondaryScreen()
                }
                "button2" -> {
                    // 处理按钮2的点击
                    showColorTest(android.graphics.Color.GREEN, "按钮2触发")
                }
                "reset" -> {
                    // 处理重置按钮
                    interactionCount = 0
                }
            }
        }
    }

    override fun onSecondaryScreenAreaClick(x: Float, y: Float) {
        runOnUiThread {
            val message = "副屏区域点击: (${x.toInt()}, ${y.toInt()})"
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }

    override fun onSecondaryScreenLongPress(duration: Long) {
        runOnUiThread {
            val message = "副屏长按: ${duration}ms"
            Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        }
    }

    override fun onSecondaryScreenSwipe(direction: String, distance: Float) {
        runOnUiThread {
            val message = "副屏滑动: $direction, ${distance.toInt()}px"
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()

            // 根据滑动方向执行不同操作
            when (direction) {
                "左滑" -> showColorTest(android.graphics.Color.BLUE, "左滑触发")
                "右滑" -> showColorTest(android.graphics.Color.RED, "右滑触发")
                "上滑" -> showTextTest()
                "下滑" -> clearSecondaryScreen()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dualScreenManager.cleanup()
    }
}
