package com.ygxj.camerademo.dualscreen

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.ygxj.camerademo.databinding.ActivityDualScreenTestBinding

/**
 * 双屏设备测试Activity
 * 用于测试和验证双屏设备的显示功能
 */
class DualScreenTestActivity : AppCompatActivity(), ClickEventHandler {

    private lateinit var binding: ActivityDualScreenTestBinding
    private lateinit var dualScreenManager: DualScreenManager
    private lateinit var deviceInfoHelper: DeviceInfoHelper
    private var interactionCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        binding = ActivityDualScreenTestBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initializeComponents()
        setupClickListeners()
        updateDeviceInfo()
        updateDualScreenStatus()
    }

    private fun initializeComponents() {
        val displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        dualScreenManager = DualScreenManager(this, displayManager)
        deviceInfoHelper = DeviceInfoHelper(this)
    }

    private fun setupClickListeners() {
        // 返回按钮
        binding.backButton.setOnClickListener {
            finish()
        }

        // 检测屏幕信息按钮
        binding.detectScreensButton.setOnClickListener {
            detectAllScreens()
        }

        // 显示红色测试按钮
        binding.showRedTestButton.setOnClickListener {
            showColorTest(android.graphics.Color.RED, "红色测试")
        }

        // 显示绿色测试按钮
        binding.showGreenTestButton.setOnClickListener {
            showColorTest(android.graphics.Color.GREEN, "绿色测试")
        }

        // 显示蓝色测试按钮
        binding.showBlueTestButton.setOnClickListener {
            showColorTest(android.graphics.Color.BLUE, "蓝色测试")
        }

        // 显示文字测试按钮
        binding.showTextTestButton.setOnClickListener {
            showTextTest()
        }

        binding.showXMLTestButton.setOnClickListener {
            showXMLLayoutTest()
        }

        // 清空副屏按钮
        binding.clearSecondaryScreenButton.setOnClickListener {
            clearSecondaryScreen()
        }
    }

    private fun updateDeviceInfo() {
        val deviceInfo = deviceInfoHelper.getDeviceInfo()
        binding.deviceInfoText.text = deviceInfo
    }

    private fun updateDualScreenStatus() {
        val statusInfo = dualScreenManager.getDualScreenStatus()
        binding.dualScreenStatusText.text = statusInfo
    }

    private fun detectAllScreens() {
        val screenInfo = dualScreenManager.getAllScreensInfo()
        binding.dualScreenStatusText.text = screenInfo
    }

    private fun showColorTest(color: Int, colorName: String) {
        val success = dualScreenManager.showColorOnSecondaryScreen(color, colorName)
        if (!success) {
            Toast.makeText(this, "未检测到副屏，无法显示内容", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showTextTest() {
        val success = dualScreenManager.showTextOnSecondaryScreen()
        if (!success) {
            Toast.makeText(this, "未检测到副屏，无法显示内容", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showXMLLayoutTest() {
        val displays = (getSystemService(DISPLAY_SERVICE) as DisplayManager).displays
        if (displays.size > 1) {
            val presentation = XmlLayoutPresentation(this, displays[1])
            presentation.show()
        } else {
            Toast.makeText(this, "未检测到副屏，无法显示内容", Toast.LENGTH_SHORT).show()
        }
    }

    private fun clearSecondaryScreen() {
        dualScreenManager.clearSecondaryScreen()
    }

    override fun onDestroy() {
        super.onDestroy()
        dualScreenManager.cleanup()
    }
}
