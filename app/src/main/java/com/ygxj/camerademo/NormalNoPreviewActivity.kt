package com.ygxj.camerademo

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.ygxj.camerademo.databinding.ActivityNoPreviewBinding
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 正常设备无预览模式拍照Activity
 * 适用于普通手机/平板的后台拍照场景，无预览界面
 */
class NormalNoPreviewActivity : AppCompatActivity() {

    private lateinit var binding: ActivityNoPreviewBinding
    private var cameraSelector: CameraSelector? = null
    private lateinit var cameraExecutor: ExecutorService
    private var availableCameras = mutableListOf<CameraInfo>()
    private var cameraProvider: ProcessCameraProvider? = null

    companion object {
        private const val TAG = "正常无预览拍照"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
        private val REQUIRED_PERMISSIONS = mutableListOf(
            Manifest.permission.CAMERA
        ).apply {
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }.toTypedArray()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        binding = ActivityNoPreviewBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        if (!checkCameraHardware()) {
            Toast.makeText(this, "设备没有相机", Toast.LENGTH_LONG).show()
            return
        }

        Log.e(TAG, "正在检查权限...")
        if (allPermissionsGranted()) {
            Log.e(TAG, "所有权限已授予，初始化相机")
            updateCameraStatus("正在初始化相机...", "#FFA726")
            initializeCameraProvider()
        } else {
            Log.e(TAG, "权限未授予，请求权限")
            updateCameraStatus("正在请求相机权限...", "#FFA726")
            requestPermissions()
        }

        setupClickListeners()
        cameraExecutor = Executors.newSingleThreadExecutor()
    }

    private fun setupClickListeners() {
        // 拍照按钮 - 直接后台拍照
        binding.captureButton.setOnClickListener { 
            Log.e(TAG, "开始正常设备后台拍照")
            captureImageDirectly()
        }

        // 切换摄像头按钮
        binding.switchCameraButton.setOnClickListener { showCameraSelectionDialog() }

        // 返回按钮
        binding.backButton.setOnClickListener { finish() }

        // 缩略图点击事件
        binding.thumbnailContainer.setOnClickListener {
            Toast.makeText(this, "点击了照片缩略图", Toast.LENGTH_SHORT).show()
        }
    }

    private fun captureImageDirectly() {
        Log.e(TAG, "开始正常设备后台拍照...")

        if (cameraSelector == null) {
            Log.e(TAG, "拍照失败: 摄像头选择器为空")
            updateCameraStatus("请先选择摄像头", "#F44336")
            Toast.makeText(this, "请先选择摄像头", Toast.LENGTH_SHORT).show()
            showCameraSelectionDialog()
            return
        }

        updateCameraStatus("正在拍照...", "#2196F3")
        captureFromCameraStream()
    }

    private fun captureFromCameraStream() {
        Log.e(TAG, "开始从相机流捕获图像...")
        
        try {
            val provider = cameraProvider
            val currentCameraSelector = cameraSelector

            if (provider != null && currentCameraSelector != null) {
                val imageAnalyzer = ImageAnalysis.Builder()
                    .setTargetResolution(android.util.Size(1920, 1080))
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .setImageQueueDepth(1)
                    .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888)
                    .build()

                imageAnalyzer.setAnalyzer(cameraExecutor) { imageProxy ->
                    Log.e(TAG, "从相机获取到图像数据")
                    cameraExecutor.execute {
                        processNormalCameraImage(imageProxy)
                    }
                    imageAnalyzer.clearAnalyzer()
                }

                provider.unbindAll()
                provider.bindToLifecycle(this, currentCameraSelector, imageAnalyzer)

                Log.e(TAG, "正常设备后台拍照已启动")

            } else {
                Log.e(TAG, "相机提供者或选择器为空")
                runOnUiThread {
                    updateCameraStatus("相机未准备就绪", "#F44336")
                    Toast.makeText(this@NormalNoPreviewActivity, "相机未准备就绪", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "正常设备后台拍照失败", e)
            runOnUiThread {
                updateCameraStatus("拍照失败", "#F44336")
                Toast.makeText(this@NormalNoPreviewActivity, "拍照失败: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun processNormalCameraImage(imageProxy: ImageProxy) {
        try {
            Log.e(TAG, "开始处理正常设备图像...")
            debugImageProxy(imageProxy)

            val bitmap = when (imageProxy.format) {
                android.graphics.ImageFormat.YUV_420_888 -> {
                    Log.e(TAG, "检测到YUV_420_888格式，使用标准转换")
                    convertYuvToBitmap(imageProxy) ?: convertYuvToRgbBitmap(imageProxy)
                }
                android.graphics.ImageFormat.JPEG -> {
                    Log.e(TAG, "检测到JPEG格式，直接解码")
                    val buffer = imageProxy.planes[0].buffer
                    val bytes = ByteArray(buffer.remaining())
                    buffer.get(bytes)
                    android.graphics.BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
                }
                else -> {
                    Log.e(TAG, "未知格式: ${imageProxy.format}，尝试YUV转换")
                    convertYuvToBitmap(imageProxy) ?: convertYuvToRgbBitmap(imageProxy)
                }
            }

            if (bitmap != null) {
                saveNormalCameraBitmap(bitmap)
                runOnUiThread {
                    Log.e(TAG, "正常设备后台拍照成功")
                    updateCameraStatus("拍照成功！准备下次拍照", "#4CAF50")
                    Toast.makeText(this@NormalNoPreviewActivity, "拍照成功！", Toast.LENGTH_SHORT).show()
                    
                    // 3秒后恢复就绪状态
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        if (cameraSelector != null) {
                            val cameraName = availableCameras.firstOrNull()?.let { getCameraDisplayName(it) } ?: "摄像头"
                            updateCameraStatus("$cameraName 已就绪，可以拍照", "#4CAF50")
                        }
                    }, 3000)
                }
            } else {
                Log.e(TAG, "无法转换图像数据为bitmap")
                runOnUiThread {
                    updateCameraStatus("图像转换失败", "#F44336")
                    Toast.makeText(this@NormalNoPreviewActivity, "图像格式转换失败", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理正常设备图像失败", e)
            runOnUiThread {
                updateCameraStatus("图像处理失败", "#F44336")
                Toast.makeText(this@NormalNoPreviewActivity, "图像处理失败", Toast.LENGTH_SHORT).show()
            }
        } finally {
            imageProxy.close()
        }
    }

    private fun debugImageProxy(imageProxy: ImageProxy) {
        Log.e(TAG, "=== 正常设备图像信息 ===")
        Log.e(TAG, "图像格式: ${imageProxy.format}")
        Log.e(TAG, "图像尺寸: ${imageProxy.width} x ${imageProxy.height}")
        Log.e(TAG, "平面数量: ${imageProxy.planes.size}")
        
        imageProxy.planes.forEachIndexed { index, plane ->
            Log.e(TAG, "平面 $index: 像素步长=${plane.pixelStride}, 行步长=${plane.rowStride}")
        }
        Log.e(TAG, "========================")
    }

    private fun saveNormalCameraBitmap(bitmap: android.graphics.Bitmap) {
        try {
            val name = SimpleDateFormat(FILENAME_FORMAT, Locale.US)
                .format(System.currentTimeMillis())
            val photoFile = File(getExternalFilesDir(null), "$name.jpg")

            photoFile.parentFile?.let { parentDir ->
                if (!parentDir.exists()) {
                    parentDir.mkdirs()
                }
            }

            photoFile.outputStream().use { out ->
                bitmap.compress(android.graphics.Bitmap.CompressFormat.JPEG, 95, out)
            }

            val savedUri = android.net.Uri.fromFile(photoFile)

            runOnUiThread {
                Log.e(TAG, "正常设备图片保存成功: $savedUri")
                showThumbnail(savedUri)

                try {
                    val mediaScanIntent = android.content.Intent(android.content.Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
                    mediaScanIntent.data = savedUri
                    sendBroadcast(mediaScanIntent)
                } catch (e: Exception) {
                    Log.e(TAG, "通知媒体扫描器失败", e)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "保存正常设备图片失败", e)
            runOnUiThread {
                Toast.makeText(this@NormalNoPreviewActivity, "保存图片失败", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // YUV转换方法（复用UsbCameraActivity的逻辑）
    private fun convertYuvToBitmap(imageProxy: ImageProxy): android.graphics.Bitmap? {
        return try {
            Log.e(TAG, "开始YUV转换，格式: ${imageProxy.format}, 尺寸: ${imageProxy.width}x${imageProxy.height}")

            val planes = imageProxy.planes
            val yPlane = planes[0]
            val uPlane = planes[1]
            val vPlane = planes[2]

            val yBuffer = yPlane.buffer
            val uBuffer = uPlane.buffer
            val vBuffer = vPlane.buffer

            val yRowStride = yPlane.rowStride
            val uvRowStride = uPlane.rowStride
            val uvPixelStride = uPlane.pixelStride

            val width = imageProxy.width
            val height = imageProxy.height

            val nv21Size = width * height + width * height / 2
            val nv21 = ByteArray(nv21Size)

            // 复制Y平面数据
            var yPos = 0
            for (row in 0 until height) {
                val yRowStart = row * yRowStride
                for (col in 0 until width) {
                    nv21[yPos++] = yBuffer.get(yRowStart + col)
                }
            }

            // 处理UV平面
            var uvPos = width * height
            val uvHeight = height / 2
            val uvWidth = width / 2

            for (row in 0 until uvHeight) {
                for (col in 0 until uvWidth) {
                    val uvIndex = row * uvRowStride + col * uvPixelStride

                    try {
                        nv21[uvPos++] = vBuffer.get(uvIndex) // V
                        nv21[uvPos++] = uBuffer.get(uvIndex) // U
                    } catch (e: Exception) {
                        nv21[uvPos++] = 128.toByte() // V
                        nv21[uvPos++] = 128.toByte() // U
                    }
                }
            }

            val yuvImage = android.graphics.YuvImage(
                nv21,
                android.graphics.ImageFormat.NV21,
                width,
                height,
                null
            )

            val outputStream = java.io.ByteArrayOutputStream()
            val success = yuvImage.compressToJpeg(
                android.graphics.Rect(0, 0, width, height),
                95,
                outputStream
            )

            if (success) {
                val jpegBytes = outputStream.toByteArray()
                Log.e(TAG, "YUV转换成功，大小: ${jpegBytes.size} bytes")
                android.graphics.BitmapFactory.decodeByteArray(jpegBytes, 0, jpegBytes.size)
            } else {
                Log.e(TAG, "JPEG压缩失败")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "YUV转换异常", e)
            null
        }
    }

    private fun convertYuvToRgbBitmap(imageProxy: ImageProxy): android.graphics.Bitmap? {
        return try {
            Log.e(TAG, "尝试YUV到RGB转换")

            val width = imageProxy.width
            val height = imageProxy.height
            val yPlane = imageProxy.planes[0]
            val uPlane = imageProxy.planes[1]
            val vPlane = imageProxy.planes[2]

            val yBuffer = yPlane.buffer
            val uBuffer = uPlane.buffer
            val vBuffer = vPlane.buffer

            val yRowStride = yPlane.rowStride
            val uvRowStride = uPlane.rowStride
            val uvPixelStride = uPlane.pixelStride

            val rgbPixels = IntArray(width * height)

            for (y in 0 until height) {
                for (x in 0 until width) {
                    val yIndex = y * yRowStride + x
                    val uvRow = y / 2
                    val uvCol = x / 2
                    val uvIndex = uvRow * uvRowStride + uvCol * uvPixelStride

                    val yValue = try {
                        (yBuffer.get(yIndex).toInt() and 0xFF)
                    } catch (e: Exception) {
                        128
                    }

                    val uValue = try {
                        (uBuffer.get(uvIndex).toInt() and 0xFF) - 128
                    } catch (e: Exception) {
                        0
                    }

                    val vValue = try {
                        (vBuffer.get(uvIndex).toInt() and 0xFF) - 128
                    } catch (e: Exception) {
                        0
                    }

                    val r = (yValue + 1.402 * vValue).toInt().coerceIn(0, 255)
                    val g = (yValue - 0.344136 * uValue - 0.714136 * vValue).toInt().coerceIn(0, 255)
                    val b = (yValue + 1.772 * uValue).toInt().coerceIn(0, 255)

                    rgbPixels[y * width + x] = android.graphics.Color.rgb(r, g, b)
                }
            }

            val bitmap = android.graphics.Bitmap.createBitmap(width, height, android.graphics.Bitmap.Config.ARGB_8888)
            bitmap.setPixels(rgbPixels, 0, width, 0, 0, width, height)

            Log.e(TAG, "RGB转换成功")
            bitmap

        } catch (e: Exception) {
            Log.e(TAG, "RGB转换失败", e)
            null
        }
    }

    // 相机管理方法
    private fun initializeCameraProvider() {
        Log.e(TAG, "正在初始化相机提供者...")
        updateCameraStatus("正在获取摄像头提供者...", "#FFA726")

        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            try {
                this.cameraProvider = cameraProviderFuture.get()
                Log.e(TAG, "相机提供者获取成功")
                updateCameraStatus("正在检测可用摄像头...", "#FFA726")

                getAllAvailableCameras()
                // 强制用户选择摄像头
                showCameraSelectionDialog()

            } catch(exc: Exception) {
                Log.e(TAG, "相机提供者初始化失败", exc)
                updateCameraStatus("摄像头初始化失败: ${exc.message}", "#F44336")
                Toast.makeText(this, "相机初始化失败: ${exc.message}", Toast.LENGTH_LONG).show()
            }

        }, ContextCompat.getMainExecutor(this))
    }

    private fun getAllAvailableCameras() {
        cameraProvider?.let { provider ->
            availableCameras.clear()
            val cameraInfos = provider.availableCameraInfos
            Log.e(TAG, "设备上发现 ${cameraInfos.size} 个摄像头")

            cameraInfos.forEachIndexed { index, cameraInfo ->
                availableCameras.add(cameraInfo)
                val lensFacingName = when (cameraInfo.lensFacing) {
                    CameraSelector.LENS_FACING_FRONT -> "前置"
                    CameraSelector.LENS_FACING_BACK -> "后置"
                    CameraSelector.LENS_FACING_EXTERNAL -> "外置"
                    else -> "未知"
                }
                Log.e(TAG, "摄像头 $index: 朝向=$lensFacingName (${cameraInfo.lensFacing})")
            }

            if (availableCameras.isEmpty()) {
                Log.e(TAG, "警告: 没有找到可用的摄像头")
                updateCameraStatus("未找到可用摄像头", "#F44336")
                Toast.makeText(this, "没有找到可用的摄像头", Toast.LENGTH_LONG).show()
            } else {
                updateCameraStatus("发现 ${availableCameras.size} 个摄像头，请选择...", "#FFA726")
            }
        }
    }

    private fun showCameraSelectionDialog() {
        if (availableCameras.isEmpty()) {
            Toast.makeText(this, "正在获取摄像头信息，请稍后再试", Toast.LENGTH_SHORT).show()
            return
        }

        val cameraNames = availableCameras.mapIndexed { index, cameraInfo ->
            val lensFacing = when (cameraInfo.lensFacing) {
                CameraSelector.LENS_FACING_FRONT -> "前置摄像头"
                CameraSelector.LENS_FACING_BACK -> "后置摄像头"
                CameraSelector.LENS_FACING_EXTERNAL -> "外置摄像头"
                else -> "未知摄像头"
            }
            "$lensFacing (索引: $index)"
        }.toTypedArray()

        AlertDialog.Builder(this)
            .setTitle("选择摄像头")
            .setItems(cameraNames) { _, which ->
                selectCamera(which)
            }
            .show()
    }

    private fun selectCamera(cameraIndex: Int) {
        if (cameraIndex < 0 || cameraIndex >= availableCameras.size) {
            Toast.makeText(this, "无效的摄像头选择", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedCamera = availableCameras[cameraIndex]
        Log.e(TAG, "选择摄像头: 索引=$cameraIndex, 朝向=${selectedCamera.lensFacing}")

        try {
            cameraSelector = when (selectedCamera.lensFacing) {
                CameraSelector.LENS_FACING_FRONT -> {
                    Log.e(TAG, "使用前置相机")
                    CameraSelector.DEFAULT_FRONT_CAMERA
                }
                CameraSelector.LENS_FACING_EXTERNAL -> {
                    Log.e(TAG, "使用外置摄像头")
                    CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_EXTERNAL)
                        .build()
                }
                else -> {
                    Log.e(TAG, "使用后置相机")
                    CameraSelector.DEFAULT_BACK_CAMERA
                }
            }

            val cameraName = getCameraDisplayName(selectedCamera)
            updateCameraStatus("$cameraName 已就绪，可以拍照", "#4CAF50")
            Toast.makeText(this, "已选择: $cameraName", Toast.LENGTH_SHORT).show()
            Log.e(TAG, "正常设备摄像头准备就绪，可以进行后台拍照")

        } catch (e: Exception) {
            Log.e(TAG, "创建摄像头选择器失败", e)
            updateCameraStatus("摄像头切换失败", "#F44336")
            Toast.makeText(this, "摄像头切换失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun getCameraDisplayName(cameraInfo: CameraInfo): String {
        return when (cameraInfo.lensFacing) {
            CameraSelector.LENS_FACING_FRONT -> "前置摄像头"
            CameraSelector.LENS_FACING_BACK -> "后置摄像头"
            CameraSelector.LENS_FACING_EXTERNAL -> "外置摄像头"
            else -> "未知摄像头"
        }
    }

    private fun showThumbnail(uri: android.net.Uri) {
        binding.thumbnailImage.setImageURI(uri)

        if (binding.thumbnailContainer.visibility == android.view.View.GONE) {
            binding.thumbnailContainer.visibility = android.view.View.VISIBLE
            binding.thumbnailContainer.alpha = 0f
            binding.thumbnailContainer.scaleX = 0.5f
            binding.thumbnailContainer.scaleY = 0.5f

            binding.thumbnailContainer.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(300)
                .start()
        } else {
            binding.thumbnailContainer.animate()
                .scaleX(1.1f)
                .scaleY(1.1f)
                .setDuration(100)
                .withEndAction {
                    binding.thumbnailContainer.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .start()
                }
                .start()
        }
    }

    // 状态更新方法
    private fun updateCameraStatus(message: String, color: String) {
        runOnUiThread {
            binding.cameraStatus.text = message
            binding.cameraStatus.setTextColor(android.graphics.Color.parseColor(color))
        }
    }

    // 权限管理
    private fun requestPermissions() {
        activityResultLauncher.launch(REQUIRED_PERMISSIONS)
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    private fun checkCameraHardware(): Boolean {
        return packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY)
    }

    private val activityResultLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            Log.e(TAG, "权限结果: $permissions")
            var permissionGranted = true
            permissions.entries.forEach {
                Log.e(TAG, "权限 ${it.key}: ${it.value}")
                if (it.key in REQUIRED_PERMISSIONS && it.value == false)
                    permissionGranted = false
            }
            if (!permissionGranted) {
                Log.e(TAG, "相机权限被拒绝")
                updateCameraStatus("相机权限被拒绝", "#F44336")
                Toast.makeText(baseContext, "相机权限被拒绝，无法使用相机功能", Toast.LENGTH_LONG).show()
            } else {
                Log.e(TAG, "所有权限已授予，初始化相机")
                updateCameraStatus("权限已授予，正在初始化...", "#FFA726")
                initializeCameraProvider()
            }
        }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
    }
}
