package com.ygxj.camerademo

import android.content.Intent
import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.ygxj.camerademo.databinding.ActivityMainBinding

/**
 * 主入口Activity - 提供4种拍照模式的选择
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        setupClickListeners()
    }

    private fun setupClickListeners() {
        // 正常设备有预览模式
        binding.normalPreviewButton.setOnClickListener {
            val intent = Intent(this, NormalPreviewActivity::class.java)
            startActivity(intent)
        }

        // 正常设备无预览模式
        binding.normalNoPreviewButton.setOnClickListener {
            val intent = Intent(this, NormalNoPreviewActivity::class.java)
            startActivity(intent)
        }

        // USB摄像头有预览模式
        binding.usbPreviewButton.setOnClickListener {
            val intent = Intent(this, UsbPreviewActivity::class.java)
            startActivity(intent)
        }

        // USB摄像头无预览模式
        binding.usbNoPreviewButton.setOnClickListener {
            val intent = Intent(this, UsbNoPreviewActivity::class.java)
            startActivity(intent)
        }
    }
}
