package com.ygxj.camerademo

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.ygxj.camerademo.databinding.ActivityPreviewBinding
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 正常设备有预览模式拍照Activity
 * 适用于普通手机/平板，提供完整的预览界面和标准拍照功能
 */
class NormalPreviewActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPreviewBinding
    private var imageCapture: ImageCapture? = null
    private var cameraSelector: CameraSelector? = null
    private lateinit var cameraExecutor: ExecutorService
    private var availableCameras = mutableListOf<CameraInfo>()
    private var cameraProvider: ProcessCameraProvider? = null

    companion object {
        private const val TAG = "正常预览拍照"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
        private val REQUIRED_PERMISSIONS = mutableListOf(
            Manifest.permission.CAMERA
        ).apply {
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }.toTypedArray()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        binding = ActivityPreviewBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        if (!checkCameraHardware()) {
            Toast.makeText(this, "设备没有相机", Toast.LENGTH_LONG).show()
            return
        }

        Log.e(TAG, "正在检查权限...")
        if (allPermissionsGranted()) {
            Log.e(TAG, "所有权限已授予，初始化相机")
            initializeCameraProvider()
        } else {
            Log.e(TAG, "权限未授予，请求权限")
            requestPermissions()
        }

        setupClickListeners()
        cameraExecutor = Executors.newSingleThreadExecutor()
    }

    private fun setupClickListeners() {
        // 拍照按钮
        binding.captureButton.setOnClickListener { 
            takePhoto()
        }

        // 切换摄像头按钮
        binding.switchCameraButton.setOnClickListener { 
            showCameraSelectionDialog() 
        }

        // 返回按钮
        binding.backButton.setOnClickListener { 
            finish() 
        }

        // 缩略图点击事件
        binding.thumbnailContainer.setOnClickListener {
            Toast.makeText(this, "点击了照片缩略图", Toast.LENGTH_SHORT).show()
        }
    }

    private fun takePhoto() {
        Log.e(TAG, "开始拍照...")

        if (cameraSelector == null) {
            Log.e(TAG, "拍照失败: 摄像头选择器为空")
            Toast.makeText(this, "请先选择摄像头", Toast.LENGTH_SHORT).show()
            showCameraSelectionDialog()
            return
        }

        val imageCapture = imageCapture ?: run {
            Log.e(TAG, "拍照失败: 图像捕获用例为空")
            Toast.makeText(this, "相机未准备就绪，请稍后再试", Toast.LENGTH_SHORT).show()
            return
        }

        val name = SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(System.currentTimeMillis())
        val photoFile = File(getExternalFilesDir(null), "$name.jpg")

        photoFile.parentFile?.let { parentDir ->
            if (!parentDir.exists()) {
                parentDir.mkdirs()
            }
        }

        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // 拍照动画
        binding.captureFlash.visibility = android.view.View.VISIBLE
        binding.captureFlash.animate()
            .alpha(1f)
            .setDuration(100)
            .withEndAction {
                binding.captureFlash.animate()
                    .alpha(0f)
                    .setDuration(100)
                    .withEndAction {
                        binding.captureFlash.visibility = android.view.View.GONE
                    }
            }

        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exception: ImageCaptureException) {
                    Log.e(TAG, "拍照失败: ${exception.message}", exception)
                    Toast.makeText(this@NormalPreviewActivity, "拍照失败: ${exception.message}", Toast.LENGTH_LONG).show()
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    val savedUri = output.savedUri ?: android.net.Uri.fromFile(photoFile)
                    Log.e(TAG, "拍照成功: $savedUri")
                    Toast.makeText(this@NormalPreviewActivity, "拍照成功", Toast.LENGTH_SHORT).show()
                    showThumbnail(savedUri)

                    // 通知媒体扫描器
                    try {
                        val mediaScanIntent = android.content.Intent(android.content.Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
                        mediaScanIntent.data = savedUri
                        sendBroadcast(mediaScanIntent)
                    } catch (e: Exception) {
                        Log.e(TAG, "通知媒体扫描器失败", e)
                    }
                }
            }
        )
    }

    private fun initializeCameraProvider() {
        Log.e(TAG, "正在初始化相机提供者...")
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            try {
                this.cameraProvider = cameraProviderFuture.get()
                Log.e(TAG, "相机提供者获取成功")
                getAllAvailableCameras()
                // 强制用户选择摄像头
                showCameraSelectionDialog()
            } catch(exc: Exception) {
                Log.e(TAG, "相机提供者初始化失败", exc)
                Toast.makeText(this, "相机初始化失败: ${exc.message}", Toast.LENGTH_LONG).show()
            }
        }, ContextCompat.getMainExecutor(this))
    }

    private fun getAllAvailableCameras() {
        cameraProvider?.let { provider ->
            availableCameras.clear()
            val cameraInfos = provider.availableCameraInfos
            Log.e(TAG, "设备上发现 ${cameraInfos.size} 个摄像头")

            cameraInfos.forEachIndexed { index, cameraInfo ->
                availableCameras.add(cameraInfo)
                val lensFacingName = when (cameraInfo.lensFacing) {
                    CameraSelector.LENS_FACING_FRONT -> "前置"
                    CameraSelector.LENS_FACING_BACK -> "后置"
                    CameraSelector.LENS_FACING_EXTERNAL -> "外置"
                    else -> "未知"
                }
                Log.e(TAG, "摄像头 $index: 朝向=$lensFacingName (${cameraInfo.lensFacing})")
            }

            if (availableCameras.isEmpty()) {
                Log.e(TAG, "警告: 没有找到可用的摄像头")
                Toast.makeText(this, "没有找到可用的摄像头", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun showCameraSelectionDialog() {
        if (availableCameras.isEmpty()) {
            Toast.makeText(this, "正在获取摄像头信息，请稍后再试", Toast.LENGTH_SHORT).show()
            return
        }

        val cameraNames = availableCameras.mapIndexed { index, cameraInfo ->
            val lensFacing = when (cameraInfo.lensFacing) {
                CameraSelector.LENS_FACING_FRONT -> "前置摄像头"
                CameraSelector.LENS_FACING_BACK -> "后置摄像头"
                CameraSelector.LENS_FACING_EXTERNAL -> "外置摄像头"
                else -> "未知摄像头"
            }
            "$lensFacing (索引: $index)"
        }.toTypedArray()

        AlertDialog.Builder(this)
            .setTitle("选择摄像头")
            .setItems(cameraNames) { _, which ->
                selectCamera(which)
            }
            .show()
    }

    private fun selectCamera(cameraIndex: Int) {
        if (cameraIndex < 0 || cameraIndex >= availableCameras.size) {
            Toast.makeText(this, "无效的摄像头选择", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedCamera = availableCameras[cameraIndex]
        Log.e(TAG, "选择摄像头: 索引=$cameraIndex, 朝向=${selectedCamera.lensFacing}")

        try {
            cameraSelector = when (selectedCamera.lensFacing) {
                CameraSelector.LENS_FACING_FRONT -> CameraSelector.DEFAULT_FRONT_CAMERA
                CameraSelector.LENS_FACING_EXTERNAL -> {
                    CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_EXTERNAL)
                        .build()
                }
                else -> CameraSelector.DEFAULT_BACK_CAMERA
            }

            val cameraName = getCameraDisplayName(selectedCamera)
            Toast.makeText(this, "已选择: $cameraName", Toast.LENGTH_SHORT).show()
            startCamera()

        } catch (e: Exception) {
            Log.e(TAG, "创建摄像头选择器失败", e)
            Toast.makeText(this, "摄像头切换失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun getCameraDisplayName(cameraInfo: CameraInfo): String {
        return when (cameraInfo.lensFacing) {
            CameraSelector.LENS_FACING_FRONT -> "前置摄像头"
            CameraSelector.LENS_FACING_BACK -> "后置摄像头"
            CameraSelector.LENS_FACING_EXTERNAL -> "外置摄像头"
            else -> "未知摄像头"
        }
    }

    private fun startCamera() {
        val currentCameraSelector = cameraSelector
        if (currentCameraSelector == null) {
            Log.e(TAG, "摄像头选择器为空，无法启动相机")
            return
        }

        Log.e(TAG, "正在启动相机...")

        try {
            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(binding.previewView.surfaceProvider)
            }

            imageCapture = ImageCapture.Builder()
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .setJpegQuality(95)
                .build()

            val provider = cameraProvider
            if (provider == null) {
                Log.e(TAG, "相机提供者为空")
                Toast.makeText(this, "相机提供者未初始化", Toast.LENGTH_SHORT).show()
                return
            }

            provider.unbindAll()
            val camera = provider.bindToLifecycle(this, currentCameraSelector, preview, imageCapture)
            Log.e(TAG, "相机启动成功")

        } catch(exc: Exception) {
            Log.e(TAG, "用例绑定失败", exc)
            Toast.makeText(this, "相机绑定失败: ${exc.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun showThumbnail(uri: android.net.Uri) {
        binding.thumbnailImage.setImageURI(uri)

        if (binding.thumbnailContainer.visibility == android.view.View.GONE) {
            binding.thumbnailContainer.visibility = android.view.View.VISIBLE
            binding.thumbnailContainer.alpha = 0f
            binding.thumbnailContainer.scaleX = 0.5f
            binding.thumbnailContainer.scaleY = 0.5f

            binding.thumbnailContainer.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(300)
                .start()
        } else {
            binding.thumbnailContainer.animate()
                .scaleX(1.1f)
                .scaleY(1.1f)
                .setDuration(100)
                .withEndAction {
                    binding.thumbnailContainer.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .start()
                }
                .start()
        }
    }

    private fun requestPermissions() {
        activityResultLauncher.launch(REQUIRED_PERMISSIONS)
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    private fun checkCameraHardware(): Boolean {
        return packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY)
    }

    private val activityResultLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            Log.e(TAG, "权限结果: $permissions")
            var permissionGranted = true
            permissions.entries.forEach {
                Log.e(TAG, "权限 ${it.key}: ${it.value}")
                if (it.key in REQUIRED_PERMISSIONS && it.value == false)
                    permissionGranted = false
            }
            if (!permissionGranted) {
                Log.e(TAG, "相机权限被拒绝")
                Toast.makeText(baseContext, "相机权限被拒绝，无法使用相机功能", Toast.LENGTH_LONG).show()
            } else {
                Log.e(TAG, "所有权限已授予，初始化相机")
                initializeCameraProvider()
            }
        }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
    }
}
