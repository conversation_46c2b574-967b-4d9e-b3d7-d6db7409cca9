package com.ygxj.camerademo

import android.app.Activity
import android.app.Presentation
import android.content.Context
import android.graphics.Color
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.Gravity
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat.getSystemService
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.ygxj.camerademo.databinding.ActivityDualScreenTestBinding
import java.text.SimpleDateFormat
import java.util.*

/**
 * 双屏设备测试Activity
 * 用于测试和验证双屏设备的显示功能
 */
class DualScreenTestActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDualScreenTestBinding
    private var displayManager: DisplayManager? = null
    private var secondaryPresentation: SecondaryScreenPresentation? = null
    private val logMessages = mutableListOf<String>()

    companion object {
        private const val TAG = "DualScreenTest"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        binding = ActivityDualScreenTestBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initializeDisplayManager()
        setupClickListeners()
        detectDeviceInfo()
        detectDualScreenSupport()
        addLog("双屏测试Activity初始化完成")
    }

    private fun initializeDisplayManager() {
        displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        addLog("显示管理器初始化完成")
    }

    private fun setupClickListeners() {
        // 返回按钮
        binding.backButton.setOnClickListener { 
            finish() 
        }

        // 检测屏幕信息按钮
        binding.detectScreensButton.setOnClickListener { 
            detectAllScreens()
        }

        // 显示红色测试按钮
        binding.showRedTestButton.setOnClickListener { 
            showColorOnSecondaryScreen(Color.RED, "红色测试")
        }

        // 显示绿色测试按钮
        binding.showGreenTestButton.setOnClickListener { 
            showColorOnSecondaryScreen(Color.GREEN, "绿色测试")
        }

        // 显示蓝色测试按钮
        binding.showBlueTestButton.setOnClickListener { 
            showColorOnSecondaryScreen(Color.BLUE, "蓝色测试")
        }

        // 显示文字测试按钮
        binding.showTextTestButton.setOnClickListener { 
            showTextOnSecondaryScreen()
        }

        // 清空副屏按钮
        binding.clearSecondaryScreenButton.setOnClickListener { 
            clearSecondaryScreen()
        }
    }

    private fun detectDeviceInfo() {
        val deviceInfo = StringBuilder()
        
        // 设备基本信息
        deviceInfo.append("设备型号: ${android.os.Build.MODEL}\n")
        deviceInfo.append("制造商: ${android.os.Build.MANUFACTURER}\n")
        deviceInfo.append("Android版本: ${android.os.Build.VERSION.RELEASE}\n")
        deviceInfo.append("API级别: ${android.os.Build.VERSION.SDK_INT}\n")
        
        // 屏幕信息
        val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val metrics = resources.displayMetrics
        
        deviceInfo.append("主屏分辨率: ${metrics.widthPixels} x ${metrics.heightPixels}\n")
        deviceInfo.append("屏幕密度: ${metrics.density}\n")
        deviceInfo.append("DPI: ${metrics.densityDpi}\n")
        
        binding.deviceInfoText.text = deviceInfo.toString()
        addLog("设备信息检测完成")
    }

    private fun detectDualScreenSupport() {
        val displays = displayManager?.displays
        val statusText = StringBuilder()
        
        if (displays != null && displays.size > 1) {
            statusText.append("✅ 检测到多个显示器\n")
            statusText.append("显示器数量: ${displays.size}\n\n")
            
            displays.forEachIndexed { index, display ->
                statusText.append("显示器 $index:\n")
                statusText.append("  ID: ${display.displayId}\n")
                statusText.append("  名称: ${display.name}\n")
                statusText.append("  状态: ${getDisplayState(display.state)}\n")
                statusText.append("  类型: ${getDisplayType(display)}\n\n")
            }
            
            addLog("检测到 ${displays.size} 个显示器，支持双屏功能")
        } else {
            statusText.append("❌ 未检测到多个显示器\n")
            statusText.append("当前设备可能不支持双屏显示\n")
            statusText.append("或者副屏未连接/未启用")
            
            addLog("未检测到多个显示器，可能不支持双屏")
        }
        
        binding.dualScreenStatusText.text = statusText.toString()
    }

    private fun detectAllScreens() {
        addLog("开始检测所有屏幕信息...")
        
        val displays = displayManager?.displays
        if (displays != null) {
            addLog("发现 ${displays.size} 个显示器:")
            
            displays.forEachIndexed { index, display ->
                val metrics = android.util.DisplayMetrics()
                display.getMetrics(metrics)
                
                addLog("显示器 $index: ${display.name} (${metrics.widthPixels}x${metrics.heightPixels})")
            }
        } else {
            addLog("无法获取显示器信息")
        }
        
        // 重新检测双屏支持状态
        detectDualScreenSupport()
    }

    private fun showColorOnSecondaryScreen(color: Int, colorName: String) {
        val displays = displayManager?.displays
        
        if (displays != null && displays.size > 1) {
            val secondaryDisplay = displays[1] // 使用第二个显示器
            
            // 关闭之前的Presentation
            secondaryPresentation?.dismiss()
            
            // 创建新的Presentation显示颜色
            secondaryPresentation = SecondaryScreenPresentation(this, secondaryDisplay, color, colorName)
            secondaryPresentation?.show()
            
            addLog("在副屏显示${colorName}")
        } else {
            Toast.makeText(this, "未检测到副屏，无法显示内容", Toast.LENGTH_SHORT).show()
            addLog("错误: 未检测到副屏")
        }
    }

    private fun showTextOnSecondaryScreen() {
        val displays = displayManager?.displays
        
        if (displays != null && displays.size > 1) {
            val secondaryDisplay = displays[1]
            
            // 关闭之前的Presentation
            secondaryPresentation?.dismiss()
            
            // 创建显示文字的Presentation
            secondaryPresentation = SecondaryScreenPresentation(this, secondaryDisplay, Color.WHITE, "文字测试", true)
            secondaryPresentation?.show()
            
            addLog("在副屏显示测试文字")
        } else {
            Toast.makeText(this, "未检测到副屏，无法显示内容", Toast.LENGTH_SHORT).show()
            addLog("错误: 未检测到副屏")
        }
    }

    private fun clearSecondaryScreen() {
        secondaryPresentation?.dismiss()
        secondaryPresentation = null
        addLog("已清空副屏内容")
    }

    private fun getDisplayState(state: Int): String {
        return when (state) {
            Display.STATE_OFF -> "关闭"
            Display.STATE_ON -> "开启"
            Display.STATE_DOZE -> "低功耗"
            Display.STATE_DOZE_SUSPEND -> "低功耗暂停"
            Display.STATE_VR -> "VR模式"
            Display.STATE_ON_SUSPEND -> "开启暂停"
            else -> "未知($state)"
        }
    }

    private fun getDisplayType(display: Display): String {
        // 简单的显示器类型判断
        return if (display.displayId == 0) {
            "主显示器"
        } else {
            "副显示器"
        }
    }

    private fun addLog(message: String) {
        val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
        val logMessage = "[$timestamp] $message"
        
        logMessages.add(logMessage)
        
        // 保持日志数量在合理范围内
        if (logMessages.size > 50) {
            logMessages.removeAt(0)
        }
        
        // 更新UI显示
        runOnUiThread {
            binding.logText.text = logMessages.joinToString("\n")
        }
        
        // 同时输出到Logcat
        Log.e(TAG, message)
    }

    override fun onDestroy() {
        super.onDestroy()
        secondaryPresentation?.dismiss()
        addLog("双屏测试Activity已销毁")
    }
}

/**
 * 副屏显示的Presentation类
 * 用于在副屏上显示测试内容
 */
class SecondaryScreenPresentation(
    context: Context,
    display: Display,
    private val backgroundColor: Int = Color.BLACK,
    private val displayText: String = "",
    private val showText: Boolean = false
) : Presentation(context, display) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 创建副屏布局
        val layout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setBackgroundColor(backgroundColor)
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            )
        }

        if (showText) {
            // 显示文字内容
            val titleText = TextView(context).apply {
                text = "双屏测试 - 副屏显示"
                textSize = 32f
                setTextColor(Color.BLACK)
                gravity = Gravity.CENTER
                setPadding(32, 32, 32, 16)
            }

            val contentText = TextView(context).apply {
                text = """
                    这是副屏显示的测试内容

                    当前时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}

                    显示器信息:
                    • 显示器ID: ${display.displayId}
                    • 显示器名称: ${display.name}

                    测试功能:
                    ✅ 副屏内容显示
                    ✅ 文字渲染
                    ✅ 颜色显示
                    ✅ 布局适配
                """.trimIndent()
                textSize = 18f
                setTextColor(Color.DKGRAY)
                gravity = Gravity.CENTER
                setPadding(32, 16, 32, 32)
                setLineSpacing(8f, 1.0f)
            }

            layout.addView(titleText)
            layout.addView(contentText)
        } else {
            // 显示颜色和简单文字
            val colorText = TextView(context).apply {
                text = displayText
                textSize = 48f
                setTextColor(if (backgroundColor == Color.BLACK) Color.WHITE else Color.BLACK)
                gravity = Gravity.CENTER
                setPadding(32, 32, 32, 32)
            }

            layout.addView(colorText)
        }

        setContentView(layout)

        // 设置窗口属性
        window?.let { window ->
            // 在较新的Android版本中，Presentation会自动处理窗口类型
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }
}
